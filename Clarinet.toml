[project]
name = 'skillbounty-clarity'
description = ''
authors = []
telemetry = true
cache_dir = './.cache'
requirements = []
[contracts.badge-nft]
path = 'contracts/badge-nft.clar'
clarity_version = 3
epoch = 3.1

[contracts.bounty-manager]
path = 'contracts/bounty-manager.clar'
clarity_version = 3
epoch = 3.1

[contracts.dispute-resolution]
path = 'contracts/dispute-resolution.clar'
clarity_version = 3
epoch = 3.1

[contracts.endorsements]
path = 'contracts/endorsements.clar'
clarity_version = 3
epoch = 3.1

[contracts.escrow]
path = 'contracts/escrow.clar'
clarity_version = 3
epoch = 3.1

[contracts.platform-settings]
path = 'contracts/platform-settings.clar'
clarity_version = 3
epoch = 3.1

[contracts.reputation]
path = 'contracts/reputation.clar'
clarity_version = 3
epoch = 3.1

[contracts.token-credit]
path = 'contracts/token-credit.clar'
clarity_version = 3
epoch = 3.1
[repl.analysis]
passes = ['check_checker']

[repl.analysis.check_checker]
strict = false
trusted_sender = false
trusted_caller = false
callee_filter = false
